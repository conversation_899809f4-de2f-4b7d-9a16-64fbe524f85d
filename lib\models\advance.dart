import 'employee.dart';

enum AdvanceStatus {
  pending('مستحقة'),
  partiallyPaid('مسددة جزئياً'),
  fullyPaid('مسددة كاملاً');

  const AdvanceStatus(this.arabicName);
  final String arabicName;
}

class Advance {
  final int? id;
  final int employeeId;
  final double amount;
  final DateTime receivedDate;
  final String reason;
  final AdvanceStatus status;
  final double paidAmount;
  final DateTime createdAt;
  final DateTime updatedAt;

  // معلومات الموظف (للعرض فقط)
  Employee? employee;

  Advance({
    this.id,
    required this.employeeId,
    required this.amount,
    required this.receivedDate,
    required this.reason,
    this.status = AdvanceStatus.pending,
    this.paidAmount = 0.0,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.employee,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // تحويل من Map إلى Object
  factory Advance.fromMap(Map<String, dynamic> map) {
    return Advance(
      id: map['id'],
      employeeId: map['employee_id'],
      amount: map['amount']?.toDouble() ?? 0.0,
      receivedDate: DateTime.parse(map['received_date']),
      reason: map['reason'] ?? '',
      status: AdvanceStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => AdvanceStatus.pending,
      ),
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'amount': amount,
      'received_date': receivedDate.toIso8601String(),
      'reason': reason,
      'status': status.name,
      'paid_amount': paidAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من السلفة
  Advance copyWith({
    int? id,
    int? employeeId,
    double? amount,
    DateTime? receivedDate,
    String? reason,
    AdvanceStatus? status,
    double? paidAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
    Employee? employee,
  }) {
    return Advance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      amount: amount ?? this.amount,
      receivedDate: receivedDate ?? this.receivedDate,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      paidAmount: paidAmount ?? this.paidAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      employee: employee ?? this.employee,
    );
  }

  // حساب المبلغ المتبقي
  double get remainingAmount => amount - paidAmount;

  // التحقق من اكتمال السداد
  bool get isFullyPaid => paidAmount >= amount;

  // التحقق من وجود سداد جزئي
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < amount;

  // تحديث حالة السلفة بناءً على المبلغ المدفوع
  AdvanceStatus get calculatedStatus {
    if (paidAmount >= amount) {
      return AdvanceStatus.fullyPaid;
    } else if (paidAmount > 0) {
      return AdvanceStatus.partiallyPaid;
    } else {
      return AdvanceStatus.pending;
    }
  }

  @override
  String toString() {
    return 'Advance{id: $id, employeeId: $employeeId, amount: $amount, receivedDate: $receivedDate, reason: $reason, status: $status, paidAmount: $paidAmount}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Advance &&
        other.id == id &&
        other.employeeId == employeeId &&
        other.amount == amount &&
        other.receivedDate == receivedDate &&
        other.reason == reason &&
        other.status == status &&
        other.paidAmount == paidAmount;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        employeeId.hashCode ^
        amount.hashCode ^
        receivedDate.hashCode ^
        reason.hashCode ^
        status.hashCode ^
        paidAmount.hashCode;
  }
}
