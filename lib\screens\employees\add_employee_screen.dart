import 'package:flutter/material.dart';
import '../../models/employee.dart';
import '../../services/employee_service.dart';
import '../../utils/responsive_helper.dart';
import '../../theme/app_theme.dart';

class AddEmployeeScreen extends StatefulWidget {
  const AddEmployeeScreen({super.key});

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeeService _employeeService = EmployeeService();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _positionController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final employee = Employee(
        name: _nameController.text.trim(),
        position: _positionController.text.trim().isEmpty
            ? null
            : _positionController.text.trim(),
        department: _departmentController.text.trim().isEmpty
            ? null
            : _departmentController.text.trim(),
        phoneNumber: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
      );

      await _employeeService.insertEmployee(employee);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الموظف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الموظف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: CustomScrollView(
          slivers: [
            // AppBar مع تدرج
            SliverAppBar(
              expandedHeight: ResponsiveHelper.getValueForScreenSize(
                context,
                mobile: 140.0,
                tablet: 160.0,
                desktop: 180.0,
              ),
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'إضافة موظف جديد',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      mobile: 20,
                      tablet: 24,
                      desktop: 28,
                    ),
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppTheme.secondaryGradient,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                  ),
                ),
              ),
            ),

            // المحتوى الرئيسي
            SliverToBoxAdapter(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: ResponsiveHelper.getMaxContentWidth(context),
                ),
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Padding(
                  padding: ResponsiveHelper.getPadding(
                    context,
                    mobile: const EdgeInsets.all(20),
                    tablet: const EdgeInsets.all(32),
                    desktop: const EdgeInsets.all(40),
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        const SizedBox(height: 24),

                        // بطاقة المعلومات الأساسية
                        _buildBasicInfoCard(),
                        const SizedBox(height: 24),

                        // بطاقة معلومات الاتصال
                        _buildContactInfoCard(),
                        const SizedBox(height: 32),

                        // أزرار الحفظ والإلغاء
                        _buildActionButtons(),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: ResponsiveHelper.getPadding(
          context,
          mobile: const EdgeInsets.all(24),
          tablet: const EdgeInsets.all(32),
          desktop: const EdgeInsets.all(40),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: ResponsiveHelper.getPadding(
                    context,
                    mobile: const EdgeInsets.all(12),
                    tablet: const EdgeInsets.all(14),
                    desktop: const EdgeInsets.all(16),
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.person,
                    color: AppTheme.secondaryColor,
                    size: ResponsiveHelper.getIconSize(
                      context,
                      mobile: 28,
                      tablet: 32,
                      desktop: 36,
                    ),
                  ),
                ),
                SizedBox(width: ResponsiveHelper.getSpacing(context)),
                Expanded(
                  child: Text(
                    'المعلومات الأساسية',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.secondaryColor,
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            mobile: 20,
                            tablet: 24,
                            desktop: 28,
                          ),
                        ),
                  ),
                ),
              ],
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context) * 2),

            // اسم الموظف
            TextFormField(
              controller: _nameController,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
              ),
              decoration: InputDecoration(
                labelText: 'اسم الموظف *',
                hintText: 'أدخل اسم الموظف الكامل',
                prefixIcon: Icon(
                  Icons.person_outline,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                contentPadding: ResponsiveHelper.getPadding(
                  context,
                  mobile:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  tablet:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                  desktop:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الموظف';
                }
                if (value.trim().length < 2) {
                  return 'يجب أن يكون الاسم أكثر من حرف واحد';
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context) * 2),

            // المنصب
            TextFormField(
              controller: _positionController,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
              ),
              decoration: InputDecoration(
                labelText: 'المنصب',
                hintText: 'أدخل منصب الموظف',
                prefixIcon: Icon(
                  Icons.work_outline,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                contentPadding: ResponsiveHelper.getPadding(
                  context,
                  mobile:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  tablet:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                  desktop:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                ),
              ),
              textInputAction: TextInputAction.next,
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context) * 2),

            // القسم
            TextFormField(
              controller: _departmentController,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
              ),
              decoration: InputDecoration(
                labelText: 'القسم',
                hintText: 'أدخل قسم الموظف',
                prefixIcon: Icon(
                  Icons.business_outlined,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                contentPadding: ResponsiveHelper.getPadding(
                  context,
                  mobile:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  tablet:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                  desktop:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                ),
              ),
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: ResponsiveHelper.getPadding(
          context,
          mobile: const EdgeInsets.all(24),
          tablet: const EdgeInsets.all(32),
          desktop: const EdgeInsets.all(40),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: ResponsiveHelper.getPadding(
                    context,
                    mobile: const EdgeInsets.all(12),
                    tablet: const EdgeInsets.all(14),
                    desktop: const EdgeInsets.all(16),
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.contact_phone,
                    color: AppTheme.accentColor,
                    size: ResponsiveHelper.getIconSize(
                      context,
                      mobile: 28,
                      tablet: 32,
                      desktop: 36,
                    ),
                  ),
                ),
                SizedBox(width: ResponsiveHelper.getSpacing(context)),
                Expanded(
                  child: Text(
                    'معلومات الاتصال',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.accentColor,
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            mobile: 20,
                            tablet: 24,
                            desktop: 28,
                          ),
                        ),
                  ),
                ),
              ],
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context) * 2),

            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
              ),
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: 'أدخل رقم هاتف الموظف',
                prefixIcon: Icon(
                  Icons.phone_outlined,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                contentPadding: ResponsiveHelper.getPadding(
                  context,
                  mobile:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  tablet:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                  desktop:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                ),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (value.trim().length < 10) {
                    return 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل';
                  }
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),
            SizedBox(height: ResponsiveHelper.getSpacing(context) * 2),

            // البريد الإلكتروني
            TextFormField(
              controller: _emailController,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  mobile: 16,
                  tablet: 18,
                  desktop: 20,
                ),
              ),
              decoration: InputDecoration(
                labelText: 'البريد الإلكتروني',
                hintText: 'أدخل البريد الإلكتروني للموظف',
                prefixIcon: Icon(
                  Icons.email_outlined,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                contentPadding: ResponsiveHelper.getPadding(
                  context,
                  mobile:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  tablet:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                  desktop:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                ),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return 'يرجى إدخال بريد إلكتروني صحيح';
                  }
                }
                return null;
              },
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return ResponsiveWidget(
      mobile: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: ResponsiveHelper.getHeight(
              context,
              mobile: 56,
              tablet: 60,
              desktop: 64,
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveEmployee,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.secondaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'حفظ الموظف',
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          mobile: 16,
                          tablet: 18,
                          desktop: 20,
                        ),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          SizedBox(height: ResponsiveHelper.getSpacing(context)),
          SizedBox(
            width: double.infinity,
            height: ResponsiveHelper.getHeight(
              context,
              mobile: 56,
              tablet: 60,
              desktop: 64,
            ),
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(
                  color: AppTheme.secondaryColor,
                  width: 1.5,
                ),
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: ResponsiveHelper.getFontSize(
                    context,
                    mobile: 16,
                    tablet: 18,
                    desktop: 20,
                  ),
                  fontWeight: FontWeight.w600,
                  color: AppTheme.secondaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
      tablet: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: ResponsiveHelper.getHeight(
                context,
                mobile: 56,
                tablet: 60,
                desktop: 64,
              ),
              child: OutlinedButton(
                onPressed:
                    _isLoading ? null : () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: BorderSide(
                    color: AppTheme.secondaryColor,
                    width: 1.5,
                  ),
                ),
                child: Text(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      mobile: 16,
                      tablet: 18,
                      desktop: 20,
                    ),
                    fontWeight: FontWeight.w600,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: ResponsiveHelper.getSpacing(context) * 2),
          Expanded(
            child: SizedBox(
              height: ResponsiveHelper.getHeight(
                context,
                mobile: 56,
                tablet: 60,
                desktop: 64,
              ),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveEmployee,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'حفظ الموظف',
                        style: TextStyle(
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            mobile: 16,
                            tablet: 18,
                            desktop: 20,
                          ),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
